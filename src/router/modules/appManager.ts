const Layout = () => import("@/layout/index.vue");

export default {
  path: "/appManager",
  name: "AppManager",
  component: Layout,
  redirect: "/appManager/index",
  meta: {
    icon: "EP-Grid",
    title: "应用管理",
    rank: 10
  },
  children: [
    {
      path: "/appManager/index",
      name: "AppManagerIndex",
      component: () => import("@/views/appManager/index.vue"),
      meta: {
        title: "应用管理",
        showParent: true
      }
    }
  ]
} as RouteConfigsTable;
