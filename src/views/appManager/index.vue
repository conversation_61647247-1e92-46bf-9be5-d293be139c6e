<template>
  <div class="app-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>应用管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area mb-4">
        <el-form :model="searchForm" inline>
          <el-form-item label="应用名称">
            <el-input
              v-model="searchForm.appName"
              placeholder="请输入应用名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="应用编码">
            <el-input
              v-model="searchForm.appCode"
              placeholder="请输入应用编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="应用类型">
            <el-input
              v-model="searchForm.appType"
              placeholder="请输入应用类型"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="useRenderIcon('EP-Search')">
              搜索
            </el-button>
            <el-button @click="handleReset" :icon="useRenderIcon('EP-Refresh')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-area mb-4">
        <el-button type="primary" @click="handleAdd" :icon="useRenderIcon('EP-Plus')">
          新增应用
        </el-button>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        height="666"
      >
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="appName" label="应用名称" min-width="120" />
        <el-table-column prop="appCode" label="应用编码" min-width="120" />
        <el-table-column prop="appUrl" label="应用地址" min-width="200" show-overflow-tooltip />
        <!-- <el-table-column prop="appIcon" label="应用图标" min-width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.appIcon"
              :src="row.appIcon"
              style="width: 40px; height: 40px"
              fit="cover"
              :preview-src-list="[row.appIcon]"
            />
            <span v-else>-</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="appType" label="应用类型" width="100">
          <template #default="{ row }">
            {{ row.appType || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="appGroup" label="应用分组" width="100">
          <template #default="{ row }">
            {{ row.appGroup || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-area mt-4">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <AppForm
        ref="appFormRef"
        v-model="formData"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRenderIcon } from "@/components/ReIcon/src/hooks"
import AppForm from './components/AppForm.vue'
import {
  queryAppInfos,
  saveAppInfo,
  deleteAppInfo,
  type AppInfo,
  type AppManageQueryCondition
} from './api/AppManage'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref<AppInfo[]>([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive<AppManageQueryCondition>({
  appName: '',
  appCode: '',
  appType: '',
  pageNun: 1,
  pageSize: 10
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const formData = reactive<AppInfo>({
  id: '',
  appName: '',
  appCode: '',
  appUrl: '',
  appIcon: '',
  appType: '',
  appGroup: ''
})

// 表单引用
const appFormRef = ref()

// 监听formData变化
watch(
  formData,
  (newValue) => {
    console.log('主页面 formData 变化:', JSON.stringify(newValue, null, 2))
  },
  { deep: true }
)

// 获取应用列表
const getAppList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNun: pagination.current,
      pageSize: pagination.size
    }
    const response = await queryAppInfos(params)
    if (response.status === '0') {
      tableData.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取应用列表失败')
    }
  } catch (error) {
    ElMessage.error('获取应用列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getAppList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    appName: '',
    appCode: '',
    appType: '',
    pageNun: 1,
    pageSize: 10
  })
  pagination.current = 1
  getAppList()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增应用'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: AppInfo) => {
  console.log('编辑行数据:', JSON.stringify(row, null, 2))

  dialogTitle.value = '编辑应用'
  isEdit.value = true

  // 深拷贝数据进行回填
  const editData = {
    id: row.id,
    appName: row.appName,
    appCode: row.appCode,
    appUrl: row.appUrl,
    appIcon: row.appIcon,
    appType: row.appType,
    appGroup: row.appGroup
  }

  console.log('回填的数据:', JSON.stringify(editData, null, 2))
  Object.assign(formData, editData)
  console.log('formData更新后:', JSON.stringify(formData, null, 2))

  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: AppInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用"${row.appName}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteAppInfo(row.id)
    if (response.status === '0') {
      ElMessage.success('删除成功')
      getAppList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!appFormRef.value) return

  submitLoading.value = true
  try {
    const isValid = await appFormRef.value.validate()
    if (!isValid) return

    // 直接从AppForm组件获取最新的表单数据
    const submitData = appFormRef.value.getCurrentData()

    // 调试日志：查看提交的数据
    console.log('从AppForm获取的数据:', JSON.stringify(submitData, null, 2))
    console.log('主页面formData:', JSON.stringify(formData, null, 2))
    console.log('是否为编辑模式:', isEdit.value)

    const response = await saveAppInfo(submitData)
    if (response.status === '0') {
      ElMessage.success(isEdit.value ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getAppList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleDialogClose = () => {
  // 先重置父组件的数据
  resetFormData()
  // 再重置子组件的表单字段
  if (appFormRef.value) {
    appFormRef.value.resetFields()
  }
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: '',
    appName: '',
    appCode: '',
    appUrl: '',
    appIcon: '',
    appType: '',
    appGroup: ''
  })
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  getAppList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  getAppList()
}

// 组件挂载时获取数据
onMounted(() => {
  getAppList()
})
</script>

<style scoped lang="scss">
@import './styles/index.scss';
</style>
