.app-manager {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .box-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }

  .search-area {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
      
      .el-form-item__label {
        color: #fff;
        font-weight: 500;
      }
    }
    
    .el-input {
      .el-input__inner {
        background-color: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 6px;
        
        &:focus {
          background-color: #fff;
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }
      }
    }
    
    .el-button {
      border-radius: 6px;
      font-weight: 500;
      
      &.el-button--primary {
        background: linear-gradient(45deg, #409eff, #36cfc9);
        border: none;
        
        &:hover {
          background: linear-gradient(45deg, #66b1ff, #5cdbd3);
        }
      }
      
      &:not(.el-button--primary) {
        background-color: rgba(255, 255, 255, 0.9);
        color: #606266;
        border: none;
        
        &:hover {
          background-color: #fff;
          color: #409eff;
        }
      }
    }
  }

  .action-area {
    margin-bottom: 20px;
    
    .el-button {
      border-radius: 6px;
      font-weight: 500;
      padding: 12px 20px;
      
      &.el-button--primary {
        background: linear-gradient(45deg, #409eff, #36cfc9);
        border: none;
        
        &:hover {
          background: linear-gradient(45deg, #66b1ff, #5cdbd3);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .el-table__header {
      background-color: #fafafa;
      
      th {
        background-color: #fafafa !important;
        color: #606266;
        font-weight: 600;
        border-bottom: 2px solid #e4e7ed;
      }
    }
    
    .el-table__body {
      tr {
        transition: all 0.3s ease;
        
        &:hover {
          background-color: #f5f7fa;
          transform: scale(1.01);
        }
      }
      
      td {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 0;
      }
    }
    
    .el-button {
      border-radius: 4px;
      font-size: 12px;
      padding: 6px 12px;
      
      &.el-button--primary {
        background-color: #409eff;
        
        &:hover {
          background-color: #66b1ff;
        }
      }
      
      &.el-button--danger {
        background-color: #f56c6c;
        
        &:hover {
          background-color: #f78989;
        }
      }
    }
  }

  .pagination-area {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    
    .el-pagination {
      .el-pager li {
        border-radius: 4px;
        margin: 0 2px;
        
        &.active {
          background: linear-gradient(45deg, #409eff, #36cfc9);
          color: #fff;
        }
      }
      
      .btn-prev,
      .btn-next {
        border-radius: 4px;
        
        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      padding: 20px;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
      }
      
      .el-dialog__close {
        color: #fff;
        font-size: 20px;
        
        &:hover {
          color: #f0f0f0;
        }
      }
    }
    
    .el-dialog__body {
      padding: 30px;
    }
    
    .el-dialog__footer {
      padding: 20px 30px;
      background-color: #fafafa;
      
      .el-button {
        border-radius: 6px;
        padding: 10px 20px;
        font-weight: 500;
        
        &.el-button--primary {
          background: linear-gradient(45deg, #409eff, #36cfc9);
          border: none;
          
          &:hover {
            background: linear-gradient(45deg, #66b1ff, #5cdbd3);
          }
        }
      }
    }
  }

  .el-image {
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;
    
    .search-area {
      .el-form {
        .el-form-item {
          display: block;
          margin-bottom: 15px;
          
          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }
    
    .el-table {
      font-size: 12px;
      
      .el-table__body {
        td {
          padding: 12px 8px;
        }
      }
    }
    
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }
  }
}

// 暗色主题适配
.dark {
  .app-manager {
    background-color: #1a1a1a;
    
    .box-card {
      background-color: #2d2d2d;
      border: 1px solid #404040;
      
      .card-header {
        color: #e5eaf3;
      }
    }
    
    .search-area {
      background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }
    
    .el-table {
      background-color: #2d2d2d;
      
      .el-table__header th {
        background-color: #404040 !important;
        color: #e5eaf3;
        border-bottom-color: #4a5568;
      }
      
      .el-table__body {
        tr {
          background-color: #2d2d2d;
          
          &:hover {
            background-color: #404040;
          }
        }
        
        td {
          border-bottom-color: #404040;
          color: #e5eaf3;
        }
      }
    }
  }
}
