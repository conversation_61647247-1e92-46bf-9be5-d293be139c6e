import type { AppInfo, ApiResponse, PageResponse } from '../api/AppManage'

// 模拟应用数据
export const mockAppData: AppInfo[] = [
  {
    id: "1949777392239996929",
    appName: "应用管理",
    appUrl: "https://apt-daddy.name/",
    appIcon: "https://loremflickr.com/400/400?lock=7178928784630521",
    appCode: "appCode",
    appType: "web",
    appGroup: "system"
  },
  {
    id: "1949777489224888322",
    appName: "用户管理",
    appUrl: "https://user-management.example.com/",
    appIcon: "https://loremflickr.com/400/400?lock=1234567890",
    appCode: "userManage",
    appType: "web",
    appGroup: "system"
  },
  {
    id: "1949777650592346114",
    appName: "数据分析",
    appUrl: "https://analytics.example.com/",
    appIcon: "https://loremflickr.com/400/400?lock=9876543210",
    appCode: "dataAnalytics",
    appType: "web",
    appGroup: "business"
  },
  {
    id: "1949777657865269249",
    appName: "移动端应用",
    appUrl: "https://mobile.example.com/",
    appIcon: "https://loremflickr.com/400/400?lock=5555555555",
    appCode: "mobileApp",
    appType: "mobile",
    appGroup: "business"
  },
  {
    id: "22",
    appName: "API网关",
    appUrl: "https://api-gateway.example.com/",
    appIcon: "https://loremflickr.com/400/400?lock=8293184864768338",
    appCode: "apiGateway",
    appType: "api",
    appGroup: "tools"
  }
]

// 模拟分页响应
export const mockPageResponse: ApiResponse<PageResponse<AppInfo>> = {
  status: "0",
  msg: "恭喜！操作成功！",
  data: {
    records: mockAppData,
    total: 5,
    size: 10,
    current: 1,
    pages: 1
  }
}

// 模拟保存响应
export const mockSaveResponse: ApiResponse<boolean> = {
  status: "0",
  msg: "恭喜！操作成功！",
  data: true
}

// 模拟删除响应
export const mockDeleteResponse: ApiResponse<boolean> = {
  status: "0",
  msg: "恭喜！操作成功！",
  data: true
}
