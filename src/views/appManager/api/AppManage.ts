import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}`;

// 应用信息查询条件
export type AppManageQueryCondition = {
  id?: number;
  appName?: string;
  appUrl?: string;
  appIcon?: string;
  appCode?: string;
  appType?: string;
  appGroup?: string;
  pageNun?: number;
  pageSize?: number;
};

// 应用信息
export type AppInfo = {
  id: string;
  appName: string;
  appUrl: string;
  appIcon: string;
  appCode: string;
  appType: string | null;
  appGroup: string | null;
  pageNun?: number;
  pageSize?: number;
};

// 分页响应结构
export type PageResponse<T> = {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
};

// 通用响应结构
export type ApiResponse<T> = {
  status: string;
  msg: string;
  data: T;
};

/**
 * 查询应用信息列表
 * @param params 查询参数
 * @returns 应用信息列表
 */
export const queryAppInfos = (params: AppManageQueryCondition) => {
  return http.request<ApiResponse<PageResponse<AppInfo>>>(
    "post",
    `${basePath}/queryAppInfos`,
    {
      data: params
    }
  );
};

/**
 * 保存应用信息
 * @param params 应用信息
 * @returns 保存结果
 */
export const saveAppInfo = (params: AppInfo) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `${basePath}/appInfo/save`,
    {
      data: params
    }
  );
};

/**
 * 删除应用信息
 * @param id 应用ID
 * @returns 删除结果
 */
export const deleteAppInfo = (id: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `${basePath}/appInfo/delete`,
    {
      params: { id }
    }
  );
};
