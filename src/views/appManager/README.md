# 应用管理页面

## 功能概述

应用管理页面提供了完整的应用信息管理功能，包括：

- 应用信息查询与分页显示
- 应用信息新增与编辑
- 应用信息删除（带确认）
- 多条件搜索过滤
- 应用图标预览
- 响应式设计
- 暗色主题支持

## 文件结构

```
src/views/appManager/
├── api/
│   └── AppManage.ts          # API接口定义
├── components/
│   └── AppForm.vue           # 应用表单组件
├── mock/
│   └── testData.ts           # 测试数据
├── styles/
│   └── index.scss            # 样式文件
├── index.vue                 # 主页面组件
└── README.md                 # 说明文档
```

## API接口

### 1. 查询应用信息
- **接口地址**: `POST /queryAppInfos`
- **请求参数**:
  ```typescript
  {
    id?: number;
    appName?: string;
    appUrl?: string;
    appIcon?: string;
    appCode?: string;
    appType?: string;
    appGroup?: string;
    pageNun?: number;
    pageSize?: number;
  }
  ```
- **响应格式**:
  ```typescript
  {
    status: "0",
    msg: "恭喜！操作成功！",
    data: {
      records: AppInfo[],
      total: number,
      size: number,
      current: number,
      pages: number
    }
  }
  ```

### 2. 保存应用信息
- **接口地址**: `POST /appInfo/save`
- **请求参数**:
  ```typescript
  {
    id: string;
    appName: string;
    appUrl: string;
    appIcon: string;
    appCode: string;
    appType: string;
    appGroup: string;
  }
  ```
- **响应格式**:
  ```typescript
  {
    status: "0",
    msg: "恭喜！操作成功！",
    data: boolean
  }
  ```

### 3. 删除应用信息
- **接口地址**: `POST /appInfo/delete`
- **请求参数**: `id` (query参数)
- **响应格式**:
  ```typescript
  {
    status: "0",
    msg: "恭喜！操作成功！",
    data: boolean
  }
  ```

## 页面功能

### 搜索功能
- 支持按应用名称搜索
- 支持按应用编码搜索
- 支持按应用类型搜索
- 支持重置搜索条件

### 表格功能
- 分页显示应用列表
- 支持查看应用图标预览
- 支持编辑和删除操作
- 响应式表格设计

### 表单功能
- 新增应用信息
- 编辑应用信息
- 表单验证
- 必填字段检查

## 路由配置

页面路由已自动配置在 `src/router/modules/appManager.ts`：

```typescript
{
  path: "/appManager",
  name: "AppManager",
  component: Layout,
  redirect: "/appManager/index",
  meta: {
    icon: "EP-Grid",
    title: "应用管理",
    rank: 10
  },
  children: [
    {
      path: "/appManager/index",
      name: "AppManagerIndex",
      component: () => import("@/views/appManager/index.vue"),
      meta: {
        title: "应用管理",
        showParent: true
      }
    }
  ]
}
```

## 使用说明

1. 页面会在侧边栏菜单中显示为"应用管理"
2. 点击进入后可以查看所有应用信息
3. 使用搜索功能可以快速找到特定应用
4. 点击"新增应用"按钮可以添加新的应用
5. 在表格中点击"编辑"可以修改应用信息
6. 点击"删除"可以删除应用（需要确认）

## 技术栈

- Vue 3 + TypeScript
- Element Plus UI组件库
- Axios HTTP客户端
- Vue Router 路由管理

## 组件特性

### AppForm 组件
- 表单验证（应用名称、编码、地址必填）
- 应用编码格式验证（字母开头，支持字母数字下划线连字符）
- 图标预览功能
- 下拉选择应用类型和分组
- 字符长度限制和提示

### 主页面功能
- 响应式表格设计
- 图标预览和点击放大
- 加载状态指示
- 操作确认对话框
- 错误处理和用户提示

## 样式特性

- 渐变色背景设计
- 卡片式布局
- 悬停动画效果
- 响应式适配（移动端友好）
- 暗色主题支持
- 现代化UI设计

## 开发指南

### 本地开发
1. 确保项目依赖已安装
2. 启动开发服务器：`npm run dev`
3. 访问 `/appManager` 路由查看页面

### 自定义配置
- 修改 `api/AppManage.ts` 调整API接口
- 修改 `styles/index.scss` 自定义样式
- 修改 `components/AppForm.vue` 调整表单字段

### 测试数据
项目包含测试数据文件 `mock/testData.ts`，可用于开发时的数据模拟。

## 注意事项

1. 确保后端API接口已正确实现
2. 应用图标需要提供有效的URL地址
3. 删除操作不可恢复，请谨慎操作
4. 分页功能依赖后端返回正确的分页信息
5. 表单验证规则可根据实际需求调整
6. 样式文件使用SCSS，确保项目支持SCSS编译
