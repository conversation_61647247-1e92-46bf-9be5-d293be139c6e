<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
  >
    <el-form-item label="应用名称" prop="appName">
      <el-input
        v-model="formData.appName"
        placeholder="请输入应用名称"
        maxlength="50"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="应用编码" prop="appCode">
      <el-input
        v-model="formData.appCode"
        placeholder="请输入应用编码"
        maxlength="50"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="应用地址" prop="appUrl">
      <el-input
        v-model="formData.appUrl"
        placeholder="请输入应用地址"
        type="url"
      />
    </el-form-item>

    <el-form-item label="应用图标" prop="appIcon">
      <el-input
        v-model="formData.appIcon"
        placeholder="请输入应用图标地址"
        type="url"
      >
        <template #append>
          <el-button @click="previewIcon" :disabled="!formData.appIcon">
            预览
          </el-button>
        </template>
      </el-input>
      <div v-if="formData.appIcon" class="mt-2">
        <el-image
          :src="formData.appIcon"
          style="width: 50px; height: 50px"
          fit="cover"
          :preview-src-list="[formData.appIcon]"
          @error="handleImageError"
        />
      </div>
    </el-form-item>

    <el-form-item label="应用类型">
      <el-select
        v-model="formData.appType"
        placeholder="请选择应用类型"
        clearable
        style="width: 100%"
      >
        <el-option label="内部应用" value="内部应用" />
        <el-option label="外部应用" value="外部应用" />
      </el-select>
    </el-form-item>

    <el-form-item label="应用分组">
      <el-select
        v-model="formData.appGroup"
        placeholder="请选择应用分组"
        clearable
        style="width: 100%"
      >
        <el-option label="常用服务" value="常用服务" />
        <el-option label="工单管理" value="工单管理" />
        <el-option label="告警管理" value="告警管理" />
        <el-option label="态势呈现" value="态势呈现" />
        <el-option label="外部应用" value="外部应用" />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { AppInfo } from '../api/AppManage'

// Props
interface Props {
  modelValue: AppInfo
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: AppInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive<AppInfo>({
  id: '',
  appName: '',
  appCode: '',
  appUrl: '',
  appIcon: '',
  appType: '',
  appGroup: '',
  ...props.modelValue
})

// 表单验证规则
const formRules = {
  appName: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 50, message: '应用名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  appCode: [
    { required: true, message: '请输入应用编码', trigger: 'blur' }
  ],
  appUrl: [
    { required: true, message: '请输入应用地址', trigger: 'blur' }
  ]
}

// 监听表单数据变化
watch(
  formData,
  (newValue) => {
    console.log('AppForm formData 变化:', JSON.stringify(newValue, null, 2))
    emit('update:modelValue', { ...newValue })
  },
  { deep: true }
)

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('AppForm props.modelValue 变化:', JSON.stringify(newValue, null, 2))
    if (newValue) {
      Object.assign(formData, newValue)
      console.log('AppForm formData 更新后:', JSON.stringify(formData, null, 2))
    }
  },
  { deep: true, immediate: true }
)

// 预览图标
const previewIcon = () => {
  if (formData.appIcon) {
    window.open(formData.appIcon, '_blank')
  }
}

// 图片加载错误处理
const handleImageError = () => {
  ElMessage.warning('图标加载失败，请检查图标地址是否正确')
}

// 验证表单
const validate = async () => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetFields = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetFields
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.el-image {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
